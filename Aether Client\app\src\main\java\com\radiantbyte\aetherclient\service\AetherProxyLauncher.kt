package com.radiantbyte.aetherclient.service

import android.content.Context
import android.util.Log
import com.radiantbyte.aetherclient.util.MinecraftVersionDetector
import com.radiantbyte.aetherproxy.AetherProxy

/**
 * Utility to create AetherProxy instances with proper version detection
 */
object AetherProxyLauncher {

    /**
     * Create AetherProxy instance with automatic version detection
     * This is used by AetherEngine to create version-appropriate proxy instances
     */
    fun createWithVersionDetection(context: Context): AetherProxy {
        return try {
            val versionInfo = MinecraftVersionDetector.detectMinecraftVersion(context)

            if (versionInfo.isInstalled && versionInfo.versionCode > 0) {
                Log.i("AetherProxyLauncher", "Creating AetherProxy for Minecraft ${versionInfo.versionName} (code: ${versionInfo.versionCode})")

                // Use reflection to create AetherProxy with version detection
                try {
                    val aetherProxyClass = Class.forName("com.radiantbyte.aetherproxy.AetherProxy")
                    val createWithVersionCodeMethod = aetherProxyClass.getMethod("createWithVersionCode", Long::class.java)
                    createWithVersionCodeMethod.invoke(null, versionInfo.versionCode) as AetherProxy
                } catch (e: Exception) {
                    Log.w("AetherProxyLauncher", "Failed to create version-specific AetherProxy, using default: ${e.message}")
                    AetherProxy()
                }
            } else {
                Log.w("AetherProxyLauncher", "Minecraft not detected or version code invalid, using default codec")
                AetherProxy()
            }
        } catch (e: Exception) {
            Log.e("AetherProxyLauncher", "Error during AetherProxy creation: ${e.message}", e)
            AetherProxy() // Fallback to default
        }
    }
}