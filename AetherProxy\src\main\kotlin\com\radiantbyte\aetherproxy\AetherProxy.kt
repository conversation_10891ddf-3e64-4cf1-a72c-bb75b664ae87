package com.radiantbyte.aetherproxy

import com.radiantbyte.aetherproxy.definition.Definitions
import com.radiantbyte.aetherproxy.event.receiver.*
import com.radiantbyte.aetherproxy.session.AetherSession
import com.radiantbyte.aetherproxy.util.fetchAccount
import com.radiantbyte.aetherproxy.util.installAllModules
import com.radiantbyte.aetherproxy.util.refresh
import com.radiantbyte.aetherproxy.util.saveAccount
import com.radiantbyte.aetherproxy.version.VersionMapper
import com.radiantbyte.aetherproxy.version.VersionCodeMapper
import io.netty.bootstrap.Bootstrap
import io.netty.bootstrap.ServerBootstrap
import io.netty.channel.Channel
import io.netty.channel.EventLoopGroup
import io.netty.channel.nio.NioEventLoopGroup
import io.netty.channel.socket.nio.NioDatagramChannel
import net.raphimc.minecraftauth.step.bedrock.session.StepFullBedrockSession
import org.cloudburstmc.netty.channel.raknet.RakChannelFactory
import org.cloudburstmc.netty.channel.raknet.config.RakChannelOption
import org.cloudburstmc.netty.handler.codec.raknet.server.RakServerRateLimiter
import org.cloudburstmc.protocol.bedrock.BedrockPeer
import org.cloudburstmc.protocol.bedrock.BedrockPong
import org.cloudburstmc.protocol.bedrock.codec.BedrockCodec
import org.cloudburstmc.protocol.bedrock.codec.v819.Bedrock_v819
import org.cloudburstmc.protocol.bedrock.netty.initializer.BedrockChannelInitializer
import org.cloudburstmc.protocol.bedrock.packet.RequestNetworkSettingsPacket
import java.net.InetSocketAddress
import java.nio.file.Paths
import java.util.concurrent.ThreadLocalRandom

@Suppress("MemberVisibilityCanBePrivate")
class AetherProxy {

    var codec: BedrockCodec = Bedrock_v819.CODEC
        internal set

    /**
     * Set the codec based on Minecraft version string
     */
    fun setCodecForMinecraftVersion(minecraftVersion: String) {
        codec = VersionMapper.getCodecForVersion(minecraftVersion)
        println("AetherProxy: Set codec for Minecraft version $minecraftVersion -> Protocol ${codec.protocolVersion}")
        updateAdvertisement()
    }

    /**
     * Set the codec based on version code (from Android app)
     */
    fun setCodecForVersionCode(versionCode: Long) {
        val minecraftVersion = VersionCodeMapper.getBestMatchingVersion(versionCode)
        if (minecraftVersion != null) {
            setCodecForMinecraftVersion(minecraftVersion)
            println("AetherProxy: Set codec for version code $versionCode -> Minecraft $minecraftVersion -> Protocol ${codec.protocolVersion}")
        } else {
            println("AetherProxy: Unknown version code $versionCode, using default codec")
        }
    }

    /**
     * Update the advertisement with current codec information
     */
    private fun updateAdvertisement() {
        advertisement = advertisement
            .version(codec.minecraftVersion)
            .protocolVersion(codec.protocolVersion)
    }

    var advertisement: BedrockPong = BedrockPong()
        .edition("MCPE")
        .gameType("Survival")
        .version(codec.minecraftVersion)
        .protocolVersion(codec.protocolVersion)
        .motd("Aether Proxy")
        .playerCount(0)
        .maximumPlayerCount(20)
        .subMotd("A MITM proxy for Minecraft: Bedrock Edition")
        .nintendoLimited(false);

    var account: StepFullBedrockSession.FullBedrockSession? = null

    var localAddress = InetSocketAddress("0.0.0.0", 19132)

    var remoteAddress = InetSocketAddress("geo.hivebedrock.network", 19132)

    var serverEventLoopGroup: EventLoopGroup = NioEventLoopGroup(Runtime.getRuntime().availableProcessors())

    var clientEventLoopGroup: EventLoopGroup = serverEventLoopGroup

    var serverChannel: Channel? = null

    var clientChannel: Channel? = null

    val aetherSession = AetherSession(this)

    companion object {

        @JvmStatic
        fun main(args: Array<String>) {
            val waigamePasswordFile = Paths.get(".").resolve("waigamePassword.txt").toFile()
            val waigamePassword = if (waigamePasswordFile.isFile) waigamePasswordFile.readText() else null
            var account = fetchAccount()
            if (account.isExpired) {
                println("Expired account, refreshing and saving")
                account = account.refresh().also { saveAccount(it) }
            }

            Definitions.loadBlockPalette()
            val aetherProxy = AetherProxy()
            aetherProxy.account = account
            aetherProxy.localAddress = InetSocketAddress("0.0.0.0", 19132)
            aetherProxy.remoteAddress = InetSocketAddress("play.lbsg.net", 19132)

            // Check for version code argument for dynamic codec selection
            if (args.isNotEmpty()) {
                try {
                    val versionCode = args[0].toLong()
                    println("AetherProxy: Using version code from argument: $versionCode")
                    aetherProxy.setCodecForVersionCode(versionCode)
                } catch (e: NumberFormatException) {
                    // Try as Minecraft version string
                    val minecraftVersion = args[0]
                    println("AetherProxy: Using Minecraft version from argument: $minecraftVersion")
                    aetherProxy.setCodecForMinecraftVersion(minecraftVersion)
                }
            } else {
                println("AetherProxy: No version specified, using default codec (${aetherProxy.codec.minecraftVersion})")
            }

            aetherProxy.aetherSession.apply {
                proxyPassReceiver()
                definitionReceiver()
                transferReceiver()
                transferCommandReceiver()
                echoCommandReceiver()
                waigamePassword?.let { autoLoginWaiGameReceiver(it) }

                moduleManager.installAllModules()

                // Initialize client bridge for AetherClient integration
                try {
                    val proxyBridgeClass = Class.forName("com.radiantbyte.aetherclient.service.ProxyBridge")

                    // Try to get Android context for version detection
                    var androidContext: Any? = null
                    try {
                        // Try to get the application context if we're running in Android
                        val activityThreadClass = Class.forName("android.app.ActivityThread")
                        val currentApplicationMethod = activityThreadClass.getMethod("currentApplication")
                        androidContext = currentApplicationMethod.invoke(null)
                        println("AetherProxy: Found Android context for version detection")
                    } catch (e: Exception) {
                        println("AetherProxy: No Android context available (running standalone): ${e.message}")
                    }

                    val setSessionMethod = proxyBridgeClass.getMethod("setSession", Any::class.java, Class.forName("android.content.Context"))
                    setSessionMethod.invoke(null, this, androidContext)
                    println("AetherProxy: Client bridge initialized successfully")
                } catch (e: Exception) {
                    println("AetherProxy: Client bridge not available (${e.message})")
                }
            }
            aetherProxy.bootServer()

            println("Aether Proxy started at: ${aetherProxy.localAddress}")
            println("Using codec: ${aetherProxy.codec.minecraftVersion} (Protocol ${aetherProxy.codec.protocolVersion})")
        }

    }

    fun bootServer() {
        advertisement
            .ipv4Port(localAddress.port)
            .ipv6Port(localAddress.port)

        serverChannel = ServerBootstrap()
            .group(serverEventLoopGroup)
            .channelFactory(RakChannelFactory.server(NioDatagramChannel::class.java))
            .option(RakChannelOption.RAK_ADVERTISEMENT, advertisement.toByteBuf())
            .option(RakChannelOption.RAK_GUID, ThreadLocalRandom.current().nextLong())
            .option(RakChannelOption.RAK_PACKET_LIMIT, Int.MAX_VALUE)
            .option(RakChannelOption.RAK_GLOBAL_PACKET_LIMIT, Int.MAX_VALUE)
            .childHandler(object : BedrockChannelInitializer<AetherSession.InboundSession>() {
                override fun createSession0(peer: BedrockPeer, subClientId: Int): AetherSession.InboundSession {
                    return aetherSession.InboundSession(peer, subClientId)
                }

                override fun initSession(session: AetherSession.InboundSession) {}
            })
            .localAddress(localAddress)
            .bind()
            .awaitUninterruptibly()
            .channel()
            .also { it.pipeline().remove(RakServerRateLimiter::class.java) }
    }

    fun bootClient(inetSocketAddress: InetSocketAddress = remoteAddress) {
        val clientGUID = ThreadLocalRandom.current().nextLong()

        clientChannel = Bootstrap()
            .group(clientEventLoopGroup)
            .channelFactory(RakChannelFactory.client(NioDatagramChannel::class.java))
            .option(RakChannelOption.RAK_PROTOCOL_VERSION, codec.raknetProtocolVersion)
            .option(RakChannelOption.RAK_GUID, clientGUID)
            .option(RakChannelOption.RAK_REMOTE_GUID, clientGUID)
            .handler(object : BedrockChannelInitializer<AetherSession.OutboundSession>() {
                override fun createSession0(peer: BedrockPeer, subClientId: Int): AetherSession.OutboundSession {
                    return aetherSession.OutboundSession(peer, subClientId)
                }

                override fun initSession(session: AetherSession.OutboundSession) {
                    session.sendPacketImmediately(RequestNetworkSettingsPacket().apply {
                        protocolVersion = codec.protocolVersion
                    })
                }
            })
            .remoteAddress(inetSocketAddress)
            .connect()
            .awaitUninterruptibly()
            .channel()
    }

}
