package com.radiantbyte.aetherclient.service

import android.app.NotificationChannel
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.ServiceInfo
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.os.PowerManager
import android.provider.Settings
import android.util.Log
import android.view.WindowManager
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.core.app.NotificationCompat
import com.radiantbyte.aetherclient.app.AetherApp
import com.radiantbyte.aetherclient.game.AetherGuiManager
import com.radiantbyte.aetherclient.game.UserAccountHandler
import com.radiantbyte.aetherclient.model.ClientConfiguration
import com.radiantbyte.aetherclient.model.NetworkConfiguration
import com.radiantbyte.aetherclient.overlay.AetherOverlayManager
import com.radiantbyte.aetherclient.render.RenderOverlayView
import com.radiantbyte.aetherclient.service.AetherProxyLauncher
import com.radiantbyte.aetherclient.util.MinecraftVersionDetector
import com.radiantbyte.aetherproxy.AetherProxy
import com.radiantbyte.aetherproxy.definition.Definitions
import com.radiantbyte.aetherproxy.event.receiver.definitionReceiver
import com.radiantbyte.aetherproxy.event.receiver.echoCommandReceiver
import com.radiantbyte.aetherproxy.event.receiver.proxyPassReceiver
import com.radiantbyte.aetherproxy.event.receiver.transferCommandReceiver
import com.radiantbyte.aetherproxy.event.receiver.transferReceiver
import com.radiantbyte.aetherproxy.util.installAllModules
import java.net.InetSocketAddress
import kotlin.concurrent.thread
import android.app.NotificationManager as AndroidNotificationManager
import com.radiantbyte.aetherclient.ui.notification.NotificationManager as AetherNotificationManager

// Main service that manages the Aether engine
class AetherEngineService : Service() {
    companion object {
        private const val NOTIFICATION_CHANNEL_ID = "aether_engine"
        private const val NOTIFICATION_ID = 1
        var isActive = false
    }

    private lateinit var wakeLock: PowerManager.WakeLock
    private val handler = Handler(Looper.getMainLooper())

    override fun onBind(intent: Intent?): IBinder? = null

    @RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    override fun onCreate() {
        super.onCreate()

        startForegroundImmediate()

        createNotificationChannel()

        wakeLock = (getSystemService(POWER_SERVICE) as PowerManager).run {
            newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, "Aether::EngineLock").apply {
                acquire()
            }
        }

        startAetherEngine()
    }

    @RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    private fun startForegroundImmediate() {
        startForeground(
            NOTIFICATION_ID,
            createNotification().build(),
            ServiceInfo.FOREGROUND_SERVICE_TYPE_SPECIAL_USE
        )
    }

    private fun createNotificationChannel() {
        val channel = NotificationChannel(
            NOTIFICATION_CHANNEL_ID,
            "Aether Engine",
            AndroidNotificationManager.IMPORTANCE_LOW
        ).apply {
            description = "Aether background engine service"
        }

        val notificationManager = getSystemService(AndroidNotificationManager::class.java)
        notificationManager.createNotificationChannel(channel)
    }

    private fun createNotification() = NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
        .setContentTitle("Aether Engine")
        .setContentText("Enhanced gaming engine running")
        .setSmallIcon(android.R.drawable.ic_media_play)
        .setOngoing(true)
        .setPriority(NotificationCompat.PRIORITY_LOW)

    private fun startAetherEngine() {
        thread(name = "AetherEngineThread") {
            val gameSettings = AetherApp.instance.getSharedPreferences("aether_game_settings", Context.MODE_PRIVATE)
            ClientConfiguration.from(gameSettings)

            // Initialize GUI manager and load configuration
            AetherGuiManager.initialize()
            AetherGuiManager.loadGuiConfig()
            isActive = true

            // Initialize connection manager
            AetherProxyConnectionManager.initialize()

            if (Settings.canDrawOverlays(this)) {
                // Show overlay
                AetherOverlayManager.show(this)
            }

            Log.i("AetherEngine", "Engine started successfully")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        
        isActive = false
        
        if (::wakeLock.isInitialized && wakeLock.isHeld) {
            wakeLock.release()
        }

        AetherEngine.stop()
        AetherOverlayManager.hide()
        
        Log.i("AetherEngine", "Engine stopped")
    }
    
    override fun onConfigurationChanged(newConfig: android.content.res.Configuration) {
        super.onConfigurationChanged(newConfig)
        if (isActive) {
            handler.post {
                AetherOverlayManager.handleOrientationChange(this)
            }
        }
    }
}

// Engine controller object
@Suppress("MemberVisibilityCanBePrivate")
object AetherEngine {

    private val handler = Handler(Looper.getMainLooper())
    private var aetherProxy: AetherProxy? = null
    private var thread: Thread? = null
    private var renderView: RenderOverlayView? = null
    private var windowManager: WindowManager? = null

    var isActive by mutableStateOf(false)

    fun toggle(context: Context, networkConfiguration: NetworkConfiguration) {
        if (!isActive) {
            start(context, networkConfiguration)
            return
        }
        stop()
    }

    private fun start(context: Context, networkConfiguration: NetworkConfiguration) {
        if (isActive) {
            return
        }

        UserAccountHandler.init(context)
        isActive = true
        
        // Initialize connection manager
        AetherProxyConnectionManager.initialize()
        
        handler.post {
            AetherOverlayManager.show(context)
        }

        setupOverlay(context)

        this.thread = thread(
            name = "AetherProxyThread",
            priority = Thread.MAX_PRIORITY
        ) {
            runCatching {
                AetherGuiManager.loadGuiConfig()
            }.exceptionOrNull()?.let {
                it.printStackTrace()
                context.toast("Load GUI configuration error: ${it.message}")
            }

            runCatching {
                Definitions.loadBlockPalette()
            }.exceptionOrNull()?.let {
                it.printStackTrace()
                context.toast("Load block palette error: ${it.message}")
            }

            // Get selected Microsoft account
            val selectedAccount = try {
                com.radiantbyte.aetherclient.game.MicrosoftAccountManager.selectedAccount
            } catch (e: Exception) {
                Log.e("AetherEngine", "Failed to get selected account", e)
                null
            }

            // Start AetherProxy to capture game packets with version detection
            runCatching {
                // Create AetherProxy with automatic version detection
                aetherProxy = AetherProxyLauncher.createWithVersionDetection(context).apply {
                    account = selectedAccount
                    localAddress = InetSocketAddress("0.0.0.0", 19132)
                    remoteAddress = InetSocketAddress(networkConfiguration.serverHostName, networkConfiguration.serverPort)
                    
                    // Setup event receivers
                    aetherSession.apply {
                        proxyPassReceiver()
                        definitionReceiver()
                        transferReceiver()
                        transferCommandReceiver()
                        echoCommandReceiver()

                        // Install all modules
                        moduleManager.installAllModules()

                        // Register session with ProxyBridge for GUI integration
                        try {
                            ProxyBridge.setSession(this, context)
                            Log.i("AetherEngine", "ProxyBridge session registered successfully")
                        } catch (e: Exception) {
                            Log.e("AetherEngine", "Failed to register ProxyBridge session", e)
                        }
                    }

                    // Boot the proxy server
                    bootServer()

                    // Force connection manager to use real connection and show notification
                    handler.post {
                        AetherProxyConnectionManager.forceRealConnection()
                        // Show connection notification after server is successfully booted
                        AetherNotificationManager.showConnectionNotification(context)
                    }

                    Log.i("AetherEngine", "AetherProxy started at: $localAddress -> $remoteAddress")
                }
            }.exceptionOrNull()?.let {
                it.printStackTrace()
                context.toast("Start AetherProxy error: ${it.message}")
                Log.e("AetherEngine", "Failed to start AetherProxy", it)
            }
        }
    }



    fun stop() {
        if (!isActive) {
            return
        }

        isActive = false

        // Clear ProxyBridge session first to update connection state
        ProxyBridge.setSession(null)

        aetherProxy?.let {
            runCatching {
                it.serverChannel?.close()?.awaitUninterruptibly()
                it.clientChannel?.close()?.awaitUninterruptibly()
                it.serverEventLoopGroup.shutdownGracefully()
                if (it.clientEventLoopGroup != it.serverEventLoopGroup) {
                    it.clientEventLoopGroup.shutdownGracefully()
                }
                Log.i("AetherEngine", "AetherProxy stopped")
            }.onFailure { error ->
                Log.e("AetherEngine", "Error stopping AetherProxy", error)
            }
            aetherProxy = null
        }

        thread?.let {
            if (it.isAlive) {
                it.interrupt()
            }
            thread = null
        }

        // Save GUI configuration before stopping
        runCatching {
            AetherGuiManager.saveGuiConfig()
        }.onFailure { error ->
            Log.e("AetherEngine", "Failed to save GUI configuration", error)
        }

        handler.post {
            AetherOverlayManager.hide()
            // Show disconnection notification
            AetherNotificationManager.showDisconnectionNotification(AetherApp.instance)
        }

        renderView?.let { view ->
            windowManager?.removeView(view)
            renderView = null
            windowManager = null
        }

        // Reinitialize connection manager to reflect disconnected state
        AetherProxyConnectionManager.initialize()
    }

    private fun setupOverlay(context: Context) {
        try {
            windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            renderView = RenderOverlayView(context)

            val params = WindowManager.LayoutParams().apply {
                width = WindowManager.LayoutParams.MATCH_PARENT
                height = WindowManager.LayoutParams.MATCH_PARENT
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
                }
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE or
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                        WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
                format = PixelFormat.TRANSLUCENT

                // Android 12 (API 31+) specific compatibility fixes
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    // Set proper opacity for Android 12 touch handling
                    alpha = try {
                        val inputManager = context.getSystemService(Context.INPUT_SERVICE) as? android.hardware.input.InputManager
                        inputManager?.maximumObscuringOpacityForTouch ?: 0.8f
                    } catch (e: Exception) {
                        0.8f
                    }

                    // Ensure FLAG_NOT_TOUCHABLE is set for Android 12
                    flags = flags or WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE

                    // Set fit insets for proper Android 12 display handling
                    setFitInsetsTypes(0)
                    setFitInsetsSides(0)
                }
            }

            windowManager?.addView(renderView, params)
        } catch (e: Exception) {
            e.printStackTrace()
            context.toast("Failed to add render overlay view: ${e.message}")
        }
    }
}

fun Context.toast(message: String) {
    Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
}
