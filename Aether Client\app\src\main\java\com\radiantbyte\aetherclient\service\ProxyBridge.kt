package com.radiantbyte.aetherclient.service

import android.content.Context
import androidx.compose.runtime.*
import com.radiantbyte.aetherclient.util.MinecraftVersionDetector

/**
 * Static bridge for AetherProxy integration
 * This class provides a way for AetherProxy to communicate with AetherClient
 */
object ProxyBridge {
    // Static reference to the current AetherProxy session
    @Volatile
    private var currentSession: Any? = null
    
    private var _isConnected = mutableStateOf(false)
    val isConnected: State<Boolean> = _isConnected
    
    private var _modules = mutableStateOf<List<ProxyModuleInfo>>(emptyList())
    val modules: State<List<ProxyModuleInfo>> = _modules
    
    /**
     * Called by AetherProxy when it starts up
     * This should be called from AetherProxy's main initialization
     */
    @JvmStatic
    fun setSession(session: Any?, context: Context?) {
        println("ProxyBridge: setSession called with session: ${session?.javaClass?.name ?: "null"}")
        currentSession = session
        _isConnected.value = session != null

        if (session != null) {
            // Detect Minecraft version and configure AetherProxy accordingly
            context?.let { ctx ->
                try {
                    val versionInfo = MinecraftVersionDetector.detectMinecraftVersion(ctx)
                    if (versionInfo.isInstalled && versionInfo.versionCode > 0) {
                        println("ProxyBridge: Detected Minecraft version: ${versionInfo.versionName} (code: ${versionInfo.versionCode})")

                        // Try to set the codec in AetherProxy using reflection
                        try {
                            val proxyField = session.javaClass.getDeclaredField("aetherProxy")
                            proxyField.isAccessible = true
                            val aetherProxy = proxyField.get(session)

                            val setCodecMethod = aetherProxy.javaClass.getMethod("setCodecForVersionCode", Long::class.java)
                            setCodecMethod.invoke(aetherProxy, versionInfo.versionCode)

                            println("ProxyBridge: Successfully configured AetherProxy for Minecraft version ${versionInfo.versionName}")
                        } catch (e: Exception) {
                            println("ProxyBridge: Failed to configure AetherProxy codec: ${e.message}")
                            e.printStackTrace()
                        }
                    } else {
                        println("ProxyBridge: Minecraft not installed or version detection failed, using default codec")
                    }
                } catch (e: Exception) {
                    println("ProxyBridge: Error during version detection: ${e.message}")
                    e.printStackTrace()
                }
            } ?: println("ProxyBridge: No context provided for version detection")

            println("ProxyBridge: Setting session and refreshing modules")
            refreshModules()
            // Force connection manager to use real connection
            AetherProxyConnectionManager.forceRealConnection()
            // Note: Connection notification will be shown after server boots successfully
            println("ProxyBridge: Session set, real connection available")
        } else {
            _modules.value = emptyList()
            // Update connection manager to reflect disconnected state
            AetherProxyConnectionManager.initialize()
            println("ProxyBridge: Session cleared")
        }
    }

    /**
     * Legacy method for backward compatibility
     */
    @JvmStatic
    fun setSession(session: Any?) {
        setSession(session, null)
    }
    
    /**
     * Get current session (for internal use)
     */
    fun getCurrentSession(): Any? = currentSession
    
    /**
     * Refresh module list from current session
     */
    fun refreshModules() {
        val session = currentSession
        if (session == null) {
            println("ProxyBridge: No session available for refreshModules")
            _modules.value = emptyList()
            return
        }

        println("ProxyBridge: Refreshing modules from session: ${session.javaClass.name}")

        try {
            // Use reflection to get modules from the session
            val moduleManagerField = session.javaClass.getDeclaredField("moduleManager")
            moduleManagerField.isAccessible = true
            val moduleManager = moduleManagerField.get(session)
            println("ProxyBridge: Got moduleManager: ${moduleManager.javaClass.name}")

            val modulesField = try {
                moduleManager.javaClass.getDeclaredField("_modules")
            } catch (e: NoSuchFieldException) {
                println("ProxyBridge: _modules field not found, trying 'modules'")
                moduleManager.javaClass.getDeclaredField("modules")
            }
            modulesField.isAccessible = true
            val modules = modulesField.get(moduleManager) as? List<*> ?: emptyList<Any>()
            println("ProxyBridge: Found ${modules.size} modules")

            _modules.value = modules.mapNotNull { module ->
                if (module == null) return@mapNotNull null
                try {
                    // Try to get name using different approaches
                    val name = try {
                        // First try as a field
                        val nameField = module.javaClass.getDeclaredField("name")
                        nameField.isAccessible = true
                        nameField.get(module) as String
                    } catch (e: NoSuchFieldException) {
                        // If field doesn't exist, try to find it in parent classes
                        var clazz = module.javaClass
                        var nameField: java.lang.reflect.Field? = null
                        while (nameField == null) {
                            try {
                                nameField = clazz.getDeclaredField("name")
                            } catch (e: NoSuchFieldException) {
                                clazz = clazz.superclass!!
                            }
                        }
                        run {
                            nameField.isAccessible = true
                            nameField.get(module) as String
                        }
                    }

                    val categoryName = try {
                        val categoryField = module.javaClass.getDeclaredField("category")
                        categoryField.isAccessible = true
                        val category = categoryField.get(module)
                        category?.toString() ?: "Unknown"
                    } catch (e: NoSuchFieldException) {
                        var clazz = module.javaClass
                        var categoryField: java.lang.reflect.Field? = null
                        while (categoryField == null) {
                            try {
                                categoryField = clazz.getDeclaredField("category")
                            } catch (e: NoSuchFieldException) {
                                clazz = clazz.superclass!!
                            }
                        }
                        run {
                            categoryField.isAccessible = true
                            val category = categoryField.get(module)
                            category?.toString() ?: "Unknown"
                        }
                    }

                    val isEnabledMethod = module.javaClass.getMethod("isEnabled")
                    val isEnabled = isEnabledMethod.invoke(module) as Boolean

                    // Get configuration information
                    val configurations = try {
                        val getConfigMethod = module.javaClass.getMethod("getConfigurationInfo")
                        val configInfo = getConfigMethod.invoke(module) as? Map<String, Map<String, Any>>
                        configInfo?.mapValues { (_, configData) ->
                            val configName = configData["name"] as String
                            val configType = when (configData["type"] as String) {
                                "boolean" -> ConfigurationType.BOOLEAN
                                "float" -> ConfigurationType.FLOAT
                                "int" -> ConfigurationType.INT
                                "intRange" -> ConfigurationType.INT_RANGE
                                "list" -> ConfigurationType.LIST
                                else -> ConfigurationType.BOOLEAN
                            }

                            ConfigurationItem(
                                name = configName,
                                type = configType,
                                currentValue = configData["currentValue"] ?: false,
                                min = configData["min"],
                                max = configData["max"],
                                options = configData["options"] as? List<String>
                            )
                        } ?: emptyMap()
                    } catch (e: Exception) {
                        println("ProxyBridge: Failed to get configuration for module $name: ${e.message}")
                        emptyMap<String, ConfigurationItem>()
                    }

                    println("ProxyBridge: Processed module: $name ($categoryName) - ${if (isEnabled) "ON" else "OFF"} with ${configurations.size} configs")

                    ProxyModuleInfo(
                        name = name,
                        description = getModuleDescription(),
                        category = categoryName,
                        isEnabled = isEnabled,
                        configurations = configurations
                    )
                } catch (e: Exception) {
                    println("ProxyBridge: Failed to process module: ${e.message}")
                    e.printStackTrace()
                    null
                }
            }

            println("ProxyBridge: Successfully processed ${_modules.value.size} modules")
        } catch (e: Exception) {
            println("ProxyBridge: Failed to refresh modules: ${e.message}")
            e.printStackTrace()
            _modules.value = emptyList()
        }
    }
    
    /**
     * Toggle a module by name
     */
    fun toggleModule(moduleName: String): Boolean {
        val session = currentSession ?: return false
        
        return try {
            val moduleManagerField = session.javaClass.getDeclaredField("moduleManager")
            moduleManagerField.isAccessible = true
            val moduleManager = moduleManagerField.get(session)
            
            val modulesField = try {
                moduleManager.javaClass.getDeclaredField("_modules")
            } catch (e: NoSuchFieldException) {
                moduleManager.javaClass.getDeclaredField("modules")
            }
            modulesField.isAccessible = true
            val modules = modulesField.get(moduleManager) as? List<*> ?: return false
            
            val module = modules.find { module ->
                try {
                    val name = try {
                        val nameField = module?.javaClass?.getDeclaredField("name")
                        nameField?.isAccessible = true
                        nameField?.get(module) as? String
                    } catch (e: NoSuchFieldException) {
                        var clazz = module?.javaClass
                        var nameField: java.lang.reflect.Field? = null
                        while (clazz != null && nameField == null) {
                            try {
                                nameField = clazz.getDeclaredField("name")
                            } catch (e: NoSuchFieldException) {
                                clazz = clazz.superclass
                            }
                        }
                        if (nameField != null) {
                            nameField.isAccessible = true
                            nameField.get(module) as? String
                        } else {
                            null
                        }
                    }
                    name == moduleName
                } catch (e: Exception) {
                    false
                }
            } ?: return false
            
            val toggleMethod = module.javaClass.getMethod("toggle")
            toggleMethod.invoke(module)

            refreshModules()
            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * Update a module configuration by name
     */
    fun updateModuleConfiguration(moduleName: String, configName: String, value: Any): Boolean {
        val session = currentSession ?: run {
            println("ProxyBridge: No session available for updateModuleConfiguration")
            return false
        }

        println("ProxyBridge: Updating configuration '$configName' for module '$moduleName' to value: $value")

        return try {
            val moduleManagerField = session.javaClass.getDeclaredField("moduleManager")
            moduleManagerField.isAccessible = true
            val moduleManager = moduleManagerField.get(session)

            val modulesField = try {
                moduleManager.javaClass.getDeclaredField("_modules")
            } catch (e: NoSuchFieldException) {
                moduleManager.javaClass.getDeclaredField("modules")
            }
            modulesField.isAccessible = true
            val modules = modulesField.get(moduleManager) as? List<*> ?: return false

            val module = modules.find { module ->
                try {
                    val name = try {
                        val nameField = module?.javaClass?.getDeclaredField("name")
                        nameField?.isAccessible = true
                        nameField?.get(module) as? String
                    } catch (e: NoSuchFieldException) {
                        var clazz = module?.javaClass
                        var nameField: java.lang.reflect.Field? = null
                        while (clazz != null && nameField == null) {
                            try {
                                nameField = clazz.getDeclaredField("name")
                            } catch (e: NoSuchFieldException) {
                                clazz = clazz.superclass
                            }
                        }
                        if (nameField != null) {
                            nameField.isAccessible = true
                            nameField.get(module) as? String
                        } else {
                            null
                        }
                    }
                    name == moduleName
                } catch (e: Exception) {
                    false
                }
            } ?: return false

            val updateConfigMethod = module.javaClass.getMethod("updateConfiguration", String::class.java, Any::class.java)
            val result = updateConfigMethod.invoke(module, configName, value) as Boolean

            if (result) {
                println("ProxyBridge: Configuration update successful, refreshing modules")
                refreshModules()
            } else {
                println("ProxyBridge: Configuration update failed")
            }

            result
        } catch (e: Exception) {
            println("ProxyBridge: Failed to update configuration for module $moduleName: ${e.message}")
            false
        }
    }

    private fun getModuleDescription(): String {
        return "Module for enhanced gameplay"
    }
}
