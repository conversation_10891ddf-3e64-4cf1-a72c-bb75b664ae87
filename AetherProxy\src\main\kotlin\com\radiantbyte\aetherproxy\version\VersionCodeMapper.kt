package com.radiantbyte.aetherproxy.version

/**
 * Maps Android version codes to Minecraft version strings
 * Based on the versions.json data from minecraft-linux/mcpelauncher-versiondb
 */
object VersionCodeMapper {

    // Map of version codes to Minecraft version strings (only release versions, no beta)
    // This is a comprehensive mapping based on the versions.json file
    private val versionCodeToMinecraftVersion = mapOf(
        1013 to "0.1.1j",
        1015 to "0.1.1",
        1023 to "0.1.2j",
        1025 to "0.1.2",
        1033 to "0.1.3j",
        1035 to "0.1.3",
        1036 to "0.1.3",
        2006 to "0.2.0",
        2015 to "0.2.1",
        2016 to "0.2.1",
        2025 to "0.2.2",
        3007 to "0.3.0",
        3025 to "0.3.2",
        3035 to "0.3.3",
        4000 to "0.4.0j",
        4005 to "0.4.0",
        5000 to "0.5.0j",
        5004 to "0.5.0",
        5005 to "0.5.0",
        5008 to "0.5.0",
        6006 to "0.6.0",
        6009 to "0.6.0",
        30006010 to "0.6.1",
        50006010 to "0.6.1",
        30007000 to "0.7.0",
        50007000 to "0.7.0",
        30007010 to "0.7.1",
        50007010 to "0.7.1",
        30007020 to "0.7.2",
        50007020 to "0.7.2",
        30007030 to "0.7.3",
        40007030 to "0.7.3",
        50007030 to "0.7.3",
        50007040 to "0.7.4",
        70007040 to "0.7.4",
        50007050 to "0.7.5",
        70007050 to "0.7.5",
        50007060 to "0.7.6",
        70007060 to "0.7.6",
        700800001 to "0.8.0.01",
        500800010 to "0.8.0.10",
        700800010 to "0.8.0.10",
        500801011 to "0.8.1",
        700801011 to "0.8.1",
        500900000 to "0.9.0",
        500900012 to "0.9.0",
        700900012 to "0.9.0",
        500901000 to "0.9.1",
        700901000 to "0.9.1",
        500902000 to "0.9.2",
        700902000 to "0.9.2",
        500903000 to "0.9.3",
        700903000 to "0.9.3",
        500904000 to "0.9.4",
        700904000 to "0.9.4",
        500905001 to "0.9.5",
        700905001 to "0.9.5",
        740100000 to "0.10.0",
        780100000 to "0.10.0",
        740110015 to "0.11.0",
        780110015 to "0.11.0",
        740120114 to "0.12.1",
        780120114 to "0.12.1",
        740120200 to "0.12.2",
        780120200 to "0.12.2",
        740120300 to "0.12.3",
        780120300 to "0.12.3",
        740130000 to "0.13.0",
        780130000 to "0.13.0",
        740130100 to "0.13.1",
        780130100 to "0.13.1",
        740130200 to "0.13.2",
        780130200 to "0.13.2",
        740140009 to "0.14.0",
        780140009 to "0.14.0",
        740140100 to "0.14.1",
        780140100 to "0.14.1",
        740140200 to "0.14.2",
        780140200 to "0.14.2",
        740140300 to "0.14.3",
        780140300 to "0.14.3",
        870150001 to "0.15.0.1",
        871150001 to "0.15.0.1",
        870150102 to "0.15.1.2",
        871150102 to "0.15.1.2",
        870150302 to "0.15.3.2",
        871150302 to "0.15.3.2",
        870150400 to "0.15.4.0",
        871150400 to "0.15.4.0",
        870150600 to "0.15.6.0",
        871150600 to "0.15.6.0",
        870160005 to "0.16.0.5",
        871160005 to "0.16.0.5",
        870160100 to "0.16.1.0",
        871160100 to "0.16.1.0",
        870160202 to "0.16.2.2",
        871160202 to "0.16.2.2",
        871000016 to "1.0.0.16",
        872000016 to "1.0.0.16",
        871000201 to "1.0.2.1",
        872000201 to "1.0.2.1",
        871000312 to "1.0.3.12",
        872000312 to "1.0.3.12",
        871000411 to "1.0.4.11",
        872000411 to "1.0.4.11",
        871000511 to "1.0.5.11",
        872000511 to "1.0.5.11",
        871000513 to "1.0.5.13",
        872000513 to "1.0.5.13",
        871000554 to "1.0.5.54",
        872000554 to "1.0.5.54",
        871000652 to "1.0.6.52",
        872000652 to "1.0.6.52",
        871000700 to "1.0.7.0",
        872000700 to "1.0.7.0",
        871000801 to "1.0.8.1",
        872000801 to "1.0.8.1",
        871000901 to "1.0.9.1",
        872000901 to "1.0.9.1",
        871010055 to "1.1.0.55",
        872010055 to "1.1.0.55",
        871010151 to "1.1.1.51",
        872010151 to "1.1.1.51",
        871010250 to "1.1.2.50",
        872010250 to "1.1.2.50",
        871010352 to "1.1.3.52",
        872010352 to "1.1.3.52",
        871010451 to "1.1.4.51",
        872010451 to "1.1.4.51",
        871010500 to "1.1.5.0",
        872010500 to "1.1.5.0",
        871010501 to "1.1.5.1",
        872010501 to "1.1.5.1",
        871020081 to "1.2.0.81",
        872020081 to "1.2.0.81",
        871020101 to "1.2.1.1",
        872020101 to "1.2.1.1",
        871020203 to "1.2.2.3",
        872020203 to "1.2.2.3",
        871020306 to "1.2.3.6",
        872020306 to "1.2.3.6",
        871020552 to "1.2.5.52",
        872020552 to "1.2.5.52",
        871020655 to "1.2.6.55",
        872020655 to "1.2.6.55",
        871020660 to "1.2.6.60",
        872020660 to "1.2.6.60",
        871020702 to "1.2.7.2",
        872020702 to "1.2.7.2",
        871020800 to "1.2.8.0",
        872020800 to "1.2.8.0",
        871020901 to "1.2.9.1",
        872020901 to "1.2.9.1",
        871021002 to "1.2.10.2",
        872021002 to "1.2.10.2",
        871021104 to "1.2.11.4",
        872021104 to "1.2.11.4",
        871021354 to "1.2.13.54",
        872021354 to "1.2.13.54",
        871021360 to "1.2.13.60",
        872021360 to "1.2.13.60",
        871040005 to "1.4.0.5",
        872040005 to "1.4.0.5",
        871040100 to "1.4.1.0",
        872040100 to "1.4.1.0",
        871040200 to "1.4.2.0",
        872040200 to "1.4.2.0",
        871040400 to "1.4.4.0",
        872040400 to "1.4.4.0",
        871050014 to "1.5.0.14",
        872050014 to "1.5.0.14",
        871050102 to "1.5.1.2",
        872050102 to "1.5.1.2",
        871050201 to "1.5.2.1",
        872050201 to "1.5.2.1",
        871050300 to "1.5.3.0",
        872050300 to "1.5.3.0",
        871060014 to "1.6.0.14",
        872060014 to "1.6.0.14",
        871060100 to "1.6.1.0",
        872060100 to "1.6.1.0",
        871070013 to "1.7.0.13",
        872070013 to "1.7.0.13",
        871080024 to "1.8.0.24",
        872080024 to "1.8.0.24",
        871080102 to "1.8.1.2",
        872080102 to "1.8.1.2",
        871090015 to "1.9.0.15",
        872090015 to "1.9.0.15",
        871100007 to "1.10.0.7",
        872100007 to "1.10.0.7",
        871110023 to "1.11.0.23",
        872110023 to "1.11.0.23",
        871110102 to "1.11.1.2",
        872110102 to "1.11.1.2",
        871110301 to "1.11.3.1",
        872110301 to "1.11.3.1",
        871110402 to "1.11.4.2",
        872110402 to "1.11.4.2",
        871120028 to "1.12.0.28",
        872120028 to "1.12.0.28",
        871120101 to "1.12.1.1",
        872120101 to "1.12.1.1",
        941130034 to "1.13.0.34",
        942130034 to "1.13.0.34",
        941130105 to "1.13.1.5",
        942130105 to "1.13.1.5",
        941140009 to "1.14.0.9",
        942140009 to "1.14.0.9",
        941140104 to "1.14.1.4",
        942140104 to "1.14.1.4",
        941140105 to "1.14.1.5",
        942140105 to "1.14.1.5",
        941142001 to "1.14.20.1",
        942142001 to "1.14.20.1",
        941143002 to "1.14.30.2",
        942143002 to "1.14.30.2",
        941146005 to "1.14.60.5",
        942146005 to "1.14.60.5",
        943146005 to "1.14.60.5",
        944146005 to "1.14.60.5",
        941160002 to "1.16.0.2",
        942160002 to "1.16.0.2",
        943160002 to "1.16.0.2",
        944160002 to "1.16.0.2",
        941160102 to "1.16.1.02",
        942160102 to "1.16.1.02",
        943160102 to "1.16.1.02",
        944160102 to "1.16.1.02",
        941161002 to "1.16.10.02",
        942161002 to "1.16.10.02",
        943161002 to "1.16.10.02",
        944161002 to "1.16.10.02",
        941162003 to "1.16.20.03",
        942162003 to "1.16.20.03",
        943162003 to "1.16.20.03",
        944162003 to "1.16.20.03",
        941164002 to "1.16.40.02",
        942164002 to "1.16.40.02",
        943164002 to "1.16.40.02",
        944164002 to "1.16.40.02",
        951610004 to "1.16.100.04",
        961610004 to "1.16.100.04",
        971610004 to "1.16.100.04",
        981610004 to "1.16.100.04",
        951610101 to "1.16.101.01",
        961610101 to "1.16.101.01",
        971610101 to "1.16.101.01",
        981610101 to "1.16.101.01",
        951620002 to "1.16.200.02",
        961620002 to "1.16.200.02",
        971620002 to "1.16.200.02",
        981620002 to "1.16.200.02",
        951620101 to "1.16.201.01",
        961620101 to "1.16.201.01",
        971620101 to "1.16.201.01",
        981620101 to "1.16.201.01",
        951621005 to "1.16.210.05",
        961621005 to "1.16.210.05",
        971621005 to "1.16.210.05",
        981621005 to "1.16.210.05",
        951622002 to "1.16.220.02",
        961622002 to "1.16.220.02",
        971622002 to "1.16.220.02",
        981622002 to "1.16.220.02",
        951622101 to "1.16.221.01",
        961622101 to "1.16.221.01",
        971622101 to "1.16.221.01",
        981622101 to "1.16.221.01",
        951700002 to "1.17.0.02",
        961700002 to "1.17.0.02",
        971700002 to "1.17.0.02",
        981700002 to "1.17.0.02",
        951700201 to "1.17.2.01",
        961700201 to "1.17.2.01",
        971700201 to "1.17.2.01",
        981700201 to "1.17.2.01",
        951701004 to "1.17.10.04",
        961701004 to "1.17.10.04",
        971701004 to "1.17.10.04",
        981701004 to "1.17.10.04",
        951701101 to "1.17.11.01",
        961701101 to "1.17.11.01",
        971701101 to "1.17.11.01",
        981701101 to "1.17.11.01",
        951703004 to "1.17.30.04",
        961703004 to "1.17.30.04",
        971703004 to "1.17.30.04",
        981703004 to "1.17.30.04",
        951703202 to "1.17.32.02",
        961703202 to "1.17.32.02",
        971703202 to "1.17.32.02",
        981703202 to "1.17.32.02",
        951703402 to "1.17.34.02",
        961703402 to "1.17.34.02",
        971703402 to "1.17.34.02",
        981703402 to "1.17.34.02",
        951704006 to "1.17.40.06",
        961704006 to "1.17.40.06",
        971704006 to "1.17.40.06",
        981704006 to "1.17.40.06",
        951704101 to "1.17.41.01",
        961704101 to "1.17.41.01",
        971704101 to "1.17.41.01",
        981704101 to "1.17.41.01",
        951800002 to "1.18.0.02",
        961800002 to "1.18.0.02",
        971800002 to "1.18.0.02",
        981800002 to "1.18.0.02",
        951800102 to "1.18.1.02",
        961800102 to "1.18.1.02",
        971800102 to "1.18.1.02",
        981800102 to "1.18.1.02",
        951800203 to "1.18.2.03",
        961800203 to "1.18.2.03",
        971800203 to "1.18.2.03",
        981800203 to "1.18.2.03",
        951801004 to "1.18.10.04",
        961801004 to "1.18.10.04",
        971801004 to "1.18.10.04",
        981801004 to "1.18.10.04",
        951801201 to "1.18.12.01",
        961801201 to "1.18.12.01",
        971801201 to "1.18.12.01",
        981801201 to "1.18.12.01",
        951803004 to "1.18.30.04",
        961803004 to "1.18.30.04",
        971803004 to "1.18.30.04",
        981803004 to "1.18.30.04",
        951803104 to "1.18.31.04",
        961803104 to "1.18.31.04",
        971803104 to "1.18.31.04",
        981803104 to "1.18.31.04",
        951900005 to "1.19.0.05",
        961900005 to "1.19.0.05",
        971900005 to "1.19.0.05",
        981900005 to "1.19.0.05",
        951900202 to "1.19.2.02",
        961900202 to "1.19.2.02",
        971900202 to "1.19.2.02",
        981900202 to "1.19.2.02",
        951901003 to "1.19.10.03",
        961901003 to "1.19.10.03",
        971901003 to "1.19.10.03",
        981901003 to "1.19.10.03",
        951901101 to "1.19.11.01",
        961901101 to "1.19.11.01",
        971901101 to "1.19.11.01",
        981901101 to "1.19.11.01",
        951902002 to "1.19.20.02",
        961902002 to "1.19.20.02",
        971902002 to "1.19.20.02",
        981902002 to "1.19.20.02",
        951902101 to "1.19.21.01",
        961902101 to "1.19.21.01",
        971902101 to "1.19.21.01",
        981902101 to "1.19.21.01",
        951902201 to "1.19.22.01",
        961902201 to "1.19.22.01",
        971902201 to "1.19.22.01",
        981902201 to "1.19.22.01",
        951903004 to "1.19.30.04",
        961903004 to "1.19.30.04",
        971903004 to "1.19.30.04",
        981903004 to "1.19.30.04",
        951903101 to "1.19.31.01",
        961903101 to "1.19.31.01",
        971903101 to "1.19.31.01",
        981903101 to "1.19.31.01",
        951904002 to "1.19.40.02",
        961904002 to "1.19.40.02",
        971904002 to "1.19.40.02",
        981904002 to "1.19.40.02",
        951904101 to "1.19.41.01",
        961904101 to "1.19.41.01",
        971904101 to "1.19.41.01",
        981904101 to "1.19.41.01",
        951905002 to "1.19.50.02",
        961905002 to "1.19.50.02",
        971905002 to "1.19.50.02",
        981905002 to "1.19.50.02",
        951905101 to "1.19.51.01",
        961905101 to "1.19.51.01",
        971905101 to "1.19.51.01",
        981905101 to "1.19.51.01",
        951906003 to "1.19.60.03",
        961906003 to "1.19.60.03",
        971906003 to "1.19.60.03",
        981906003 to "1.19.60.03",
        951906201 to "1.19.62.01",
        961906201 to "1.19.62.01",
        971906201 to "1.19.62.01",
        981906201 to "1.19.62.01",
        951906301 to "1.19.63.01",
        961906301 to "1.19.63.01",
        971906301 to "1.19.63.01",
        981906301 to "1.19.63.01",
        951907002 to "1.19.70.02",
        961907002 to "1.19.70.02",
        971907002 to "1.19.70.02",
        981907002 to "1.19.70.02",
        951907102 to "1.19.71.02",
        961907102 to "1.19.71.02",
        971907102 to "1.19.71.02",
        981907102 to "1.19.71.02",
        951907302 to "1.19.73.02",
        961907302 to "1.19.73.02",
        971907302 to "1.19.73.02",
        981907302 to "1.19.73.02",
        951908002 to "1.19.80.02",
        961908002 to "1.19.80.02",
        971908002 to "1.19.80.02",
        981908002 to "1.19.80.02",
        951908101 to "1.19.81.01",
        961908101 to "1.19.81.01",
        971908101 to "1.19.81.01",
        981908101 to "1.19.81.01",
        951908301 to "1.19.83.01",
        961908301 to "1.19.83.01",
        971908301 to "1.19.83.01",
        981908301 to "1.19.83.01",
        952000001 to "1.20.0.01",
        962000001 to "1.20.0.01",
        972000001 to "1.20.0.01",
        982000001 to "1.20.0.01",
        952000102 to "1.20.1.02",
        962000102 to "1.20.1.02",
        972000102 to "1.20.1.02",
        982000102 to "1.20.1.02",
        952001001 to "1.20.10.01",
        962001001 to "1.20.10.01",
        972001001 to "1.20.10.01",
        982001001 to "1.20.10.01",
        952001201 to "1.20.12.01",
        962001201 to "1.20.12.01",
        972001201 to "1.20.12.01",
        982001201 to "1.20.12.01",
        952001501 to "1.20.15.01",
        962001501 to "1.20.15.01",
        972001501 to "1.20.15.01",
        982001501 to "1.20.15.01",
        952003002 to "1.20.30.02",
        962003002 to "1.20.30.02",
        972003002 to "1.20.30.02",
        982003002 to "1.20.30.02",
        952003101 to "1.20.31.01",
        962003101 to "1.20.31.01",
        972003101 to "1.20.31.01",
        982003101 to "1.20.31.01",
        952003203 to "1.20.32.03",
        962003203 to "1.20.32.03",
        972003203 to "1.20.32.03",
        982003203 to "1.20.32.03",
        952004001 to "1.20.40.01",
        962004001 to "1.20.40.01",
        972004001 to "1.20.40.01",
        982004001 to "1.20.40.01",
        952004102 to "1.20.41.02",
        962004102 to "1.20.41.02",
        972004102 to "1.20.41.02",
        982004102 to "1.20.41.02",
        952005003 to "1.20.50.03",
        962005003 to "1.20.50.03",
        972005003 to "1.20.50.03",
        982005003 to "1.20.50.03",
        952005101 to "1.20.51.01",
        962005101 to "1.20.51.01",
        972005101 to "1.20.51.01",
        982005101 to "1.20.51.01",
        952006004 to "1.20.60.04",
        962006004 to "1.20.60.04",
        972006004 to "1.20.60.04",
        982006004 to "1.20.60.04",
        952007102 to "1.20.62.02",
        962007102 to "1.20.62.02",
        972007102 to "1.20.62.02",
        982007102 to "1.20.62.02",
        952007206 to "1.20.70.06",
        962007206 to "1.20.70.06",
        972007206 to "1.20.70.06",
        982007206 to "1.20.70.06",
        952007301 to "1.20.71.01",
        962007301 to "1.20.71.01",
        972007301 to "1.20.71.01",
        982007301 to "1.20.71.01",
        952007401 to "1.20.72.01",
        962007401 to "1.20.72.01",
        972007401 to "1.20.72.01",
        982007401 to "1.20.72.01",
        952007501 to "1.20.73.01",
        962007501 to "1.20.73.01",
        972007501 to "1.20.73.01",
        982007501 to "1.20.73.01",
        952008005 to "1.20.80.05",
        962008005 to "1.20.80.05",
        972008005 to "1.20.80.05",
        982008005 to "1.20.80.05",
        952008101 to "1.20.81.01",
        962008101 to "1.20.81.01",
        972008101 to "1.20.81.01",
        982008101 to "1.20.81.01",
        952100003 to "1.21.0.03",
        962100003 to "1.21.0.03",
        972100003 to "1.21.0.03",
        982100003 to "1.21.0.03",
        952100103 to "1.21.1.03",
        962100103 to "1.21.1.03",
        972100103 to "1.21.1.03",
        982100103 to "1.21.1.03",
        952100202 to "1.21.2.02",
        962100202 to "1.21.2.02",
        972100202 to "1.21.2.02",
        982100202 to "1.21.2.02",
        952102003 to "1.21.20.03",
        962102003 to "1.21.20.03",
        972102003 to "1.21.20.03",
        982102003 to "1.21.20.03",
        952102101 to "1.21.21.01",
        962102101 to "1.21.21.01",
        972102101 to "1.21.21.01",
        982102101 to "1.21.21.01",
        952102201 to "1.21.22.01",
        962102201 to "1.21.22.01",
        972102201 to "1.21.22.01",
        982102201 to "1.21.22.01",
        952102301 to "1.21.23.01",
        962102301 to "1.21.23.01",
        972102301 to "1.21.23.01",
        982102301 to "1.21.23.01",
        952103003 to "1.21.30.03",
        962103003 to "1.21.30.03",
        972103003 to "1.21.30.03",
        982103003 to "1.21.30.03",
        952103104 to "1.21.31.04",
        962103104 to "1.21.31.04",
        972103104 to "1.21.31.04",
        982103104 to "1.21.31.04",
        952105022 to "1.21.40.04",
        962105022 to "1.21.40.04",
        972105022 to "1.21.40.04",
        982105022 to "1.21.40.04",
        952105024 to "1.21.41.01",
        962105024 to "1.21.41.01",
        972105024 to "1.21.41.01",
        982105024 to "1.21.41.01",
        952105026 to "1.21.43.01",
        962105026 to "1.21.43.01",
        972105026 to "1.21.43.01",
        982105026 to "1.21.43.01",
        952105028 to "1.21.44.01",
        962105028 to "1.21.44.01",
        972105028 to "1.21.44.01",
        982105028 to "1.21.44.01",
        952105047 to "1.21.50.07",
        962105047 to "1.21.50.07",
        972105047 to "1.21.50.07",
        982105047 to "1.21.50.07",
        952105101 to "1.21.51.01",
        962105101 to "1.21.51.01",
        972105101 to "1.21.51.01",
        982105101 to "1.21.51.01",
        972105102 to "1.21.51.02",
        952106010 to "1.21.60.10",
        962106010 to "1.21.60.10",
        972106010 to "1.21.60.10",
        982106010 to "1.21.60.10",
        952106101 to "1.21.61.01",
        962106101 to "1.21.61.01",
        972106101 to "1.21.61.01",
        982106101 to "1.21.61.01",
        952106201 to "1.21.62.01",
        962106201 to "1.21.62.01",
        972106201 to "1.21.62.01",
        982106201 to "1.21.62.01",
        952107003 to "1.21.70.03",
        962107003 to "1.21.70.03",
        972107003 to "1.21.70.03",
        982107003 to "1.21.70.03",
        952107101 to "1.21.71.01",
        962107101 to "1.21.71.01",
        972107101 to "1.21.71.01",
        982107101 to "1.21.71.01",
        952107201 to "1.21.72.01",
        962107201 to "1.21.72.01",
        972107201 to "1.21.72.01",
        982107201 to "1.21.72.01",
        952108003 to "1.21.80.3",
        962108003 to "1.21.80.3",
        972108003 to "1.21.80.3",
        982108003 to "1.21.80.3",
        952108102 to "1.21.81.2",
        962108102 to "1.21.81.2",
        972108102 to "1.21.81.2",
        982108102 to "1.21.81.2",
        952109003 to "1.21.90.3",
        962109003 to "1.21.90.3",
        972109003 to "1.21.90.3",
        982109003 to "1.21.90.3",
        952109201 to "1.21.92.1",
        962109201 to "1.21.92.1",
        972109201 to "1.21.92.1",
        982109201 to "1.21.92.1",
        952109301 to "1.21.93.1",
        962109301 to "1.21.93.1",
        972109301 to "1.21.93.1",
        982109301 to "1.21.93.1",
        952109401 to "1.21.94.1",
        962109401 to "1.21.94.1",
        972109401 to "1.21.94.1",
        982109401 to "1.21.94.1"
    )

    /**
     * Get the Minecraft version string for a given version code
     */
    fun getMinecraftVersionForCode(versionCode: Long): String? {
        return versionCodeToMinecraftVersion[versionCode.toInt()]
    }

    /**
     * Get the Minecraft version string for a given version code (Int version)
     */
    fun getMinecraftVersionForCode(versionCode: Int): String? {
        return versionCodeToMinecraftVersion[versionCode]
    }

    /**
     * Check if a version code is supported
     */
    fun isVersionCodeSupported(versionCode: Long): Boolean {
        return versionCodeToMinecraftVersion.containsKey(versionCode.toInt())
    }

    /**
     * Check if a version code is supported (Int version)
     */
    fun isVersionCodeSupported(versionCode: Int): Boolean {
        return versionCodeToMinecraftVersion.containsKey(versionCode)
    }

    /**
     * Get all supported version codes
     */
    fun getSupportedVersionCodes(): Set<Int> {
        return versionCodeToMinecraftVersion.keys
    }

    /**
     * Get the best matching Minecraft version for a version code
     * If exact match is not found, tries to find the closest lower version
     */
    fun getBestMatchingVersion(versionCode: Long): String? {
        val code = versionCode.toInt()

        // Try exact match first
        versionCodeToMinecraftVersion[code]?.let { return it }

        // Find the highest version code that is lower than the requested one
        val lowerVersions = versionCodeToMinecraftVersion.keys.filter { it <= code }
        if (lowerVersions.isNotEmpty()) {
            val closestCode = lowerVersions.maxOrNull()
            return closestCode?.let { versionCodeToMinecraftVersion[it] }
        }

        return null
    }

    /**
     * Get the best matching Minecraft version for a version code (Int version)
     */
    fun getBestMatchingVersion(versionCode: Int): String? {
        return getBestMatchingVersion(versionCode.toLong())
    }
}