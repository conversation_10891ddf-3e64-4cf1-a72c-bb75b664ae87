package com.radiantbyte.aetherproxy.version

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

class VersionCodeMapperTest {

    @Test
    fun testGetMinecraftVersionForCode() {
        // Test some known version codes
        assertEquals("1.16.20.03", VersionCodeMapper.getMinecraftVersionForCode(941162003))
        assertEquals("1.16.20.03", VersionCodeMapper.getMinecraftVersionForCode(942162003))
        assertEquals("1.16.20.03", VersionCodeMapper.getMinecraftVersionForCode(943162003))
        assertEquals("1.16.20.03", VersionCodeMapper.getMinecraftVersionForCode(944162003))

        // Test with Long version
        assertEquals("1.16.20.03", VersionCodeMapper.getMinecraftVersionForCode(941162003L))
    }

    @Test
    fun testIsVersionCodeSupported() {
        assertTrue(VersionCodeMapper.isVersionCodeSupported(941162003))
        assertTrue(VersionCodeMapper.isVersionCodeSupported(942162003))
        assertFalse(VersionCodeMapper.isVersionCodeSupported(999999999))

        // Test with Long version
        assertTrue(VersionCodeMapper.isVersionCodeSupported(941162003L))
        assertFalse(VersionCodeMapper.isVersionCodeSupported(999999999L))
    }

    @Test
    fun testGetSupportedVersionCodes() {
        val supportedCodes = VersionCodeMapper.getSupportedVersionCodes()
        assertTrue(supportedCodes.contains(941162003))
        assertTrue(supportedCodes.contains(942162003))
        assertTrue(supportedCodes.size > 100) // Should have many version codes
    }

    @Test
    fun testGetBestMatchingVersion() {
        // Test exact match
        assertEquals("1.16.20.03", VersionCodeMapper.getBestMatchingVersion(941162003))

        // Test with a code that's higher than existing ones but should find closest lower
        val higherCode = 950000000L
        val result = VersionCodeMapper.getBestMatchingVersion(higherCode)
        assertNotNull(result) // Should find some version

        // Test unknown version code that's lower than all existing codes
        val unknownResult = VersionCodeMapper.getBestMatchingVersion(1000)
        // For codes lower than all existing ones, it should return null
        assertNull(unknownResult)

        // Test with Long version
        assertEquals("1.16.20.03", VersionCodeMapper.getBestMatchingVersion(941162003L))
    }

    @Test
    fun testUnknownVersionCode() {
        assertNull(VersionCodeMapper.getMinecraftVersionForCode(999999999))
        assertNull(VersionCodeMapper.getMinecraftVersionForCode(999999999L))
    }
}