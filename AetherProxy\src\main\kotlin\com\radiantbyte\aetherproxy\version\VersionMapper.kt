package com.radiantbyte.aetherproxy.version

import org.cloudburstmc.protocol.bedrock.codec.BedrockCodec
import org.cloudburstmc.protocol.bedrock.codec.v291.Bedrock_v291
import org.cloudburstmc.protocol.bedrock.codec.v313.Bedrock_v313
import org.cloudburstmc.protocol.bedrock.codec.v332.Bedrock_v332
import org.cloudburstmc.protocol.bedrock.codec.v340.Bedrock_v340
import org.cloudburstmc.protocol.bedrock.codec.v354.Bedrock_v354
import org.cloudburstmc.protocol.bedrock.codec.v361.Bedrock_v361
import org.cloudburstmc.protocol.bedrock.codec.v388.Bedrock_v388
import org.cloudburstmc.protocol.bedrock.codec.v389.Bedrock_v389
import org.cloudburstmc.protocol.bedrock.codec.v390.Bedrock_v390
import org.cloudburstmc.protocol.bedrock.codec.v407.Bedrock_v407
import org.cloudburstmc.protocol.bedrock.codec.v408.Bedrock_v408
import org.cloudburstmc.protocol.bedrock.codec.v419.Bedrock_v419
import org.cloudburstmc.protocol.bedrock.codec.v422.Bedrock_v422
import org.cloudburstmc.protocol.bedrock.codec.v428.Bedrock_v428
import org.cloudburstmc.protocol.bedrock.codec.v431.Bedrock_v431
import org.cloudburstmc.protocol.bedrock.codec.v440.Bedrock_v440
import org.cloudburstmc.protocol.bedrock.codec.v448.Bedrock_v448
import org.cloudburstmc.protocol.bedrock.codec.v465.Bedrock_v465
import org.cloudburstmc.protocol.bedrock.codec.v471.Bedrock_v471
import org.cloudburstmc.protocol.bedrock.codec.v475.Bedrock_v475
import org.cloudburstmc.protocol.bedrock.codec.v486.Bedrock_v486
import org.cloudburstmc.protocol.bedrock.codec.v503.Bedrock_v503
import org.cloudburstmc.protocol.bedrock.codec.v527.Bedrock_v527
import org.cloudburstmc.protocol.bedrock.codec.v534.Bedrock_v534
import org.cloudburstmc.protocol.bedrock.codec.v544.Bedrock_v544
import org.cloudburstmc.protocol.bedrock.codec.v545.Bedrock_v545
import org.cloudburstmc.protocol.bedrock.codec.v554.Bedrock_v554
import org.cloudburstmc.protocol.bedrock.codec.v557.Bedrock_v557
import org.cloudburstmc.protocol.bedrock.codec.v560.Bedrock_v560
import org.cloudburstmc.protocol.bedrock.codec.v567.Bedrock_v567
import org.cloudburstmc.protocol.bedrock.codec.v568.Bedrock_v568
import org.cloudburstmc.protocol.bedrock.codec.v575.Bedrock_v575
import org.cloudburstmc.protocol.bedrock.codec.v582.Bedrock_v582
import org.cloudburstmc.protocol.bedrock.codec.v589.Bedrock_v589
import org.cloudburstmc.protocol.bedrock.codec.v594.Bedrock_v594
import org.cloudburstmc.protocol.bedrock.codec.v618.Bedrock_v618
import org.cloudburstmc.protocol.bedrock.codec.v622.Bedrock_v622
import org.cloudburstmc.protocol.bedrock.codec.v630.Bedrock_v630
import org.cloudburstmc.protocol.bedrock.codec.v649.Bedrock_v649
import org.cloudburstmc.protocol.bedrock.codec.v662.Bedrock_v662
import org.cloudburstmc.protocol.bedrock.codec.v671.Bedrock_v671
import org.cloudburstmc.protocol.bedrock.codec.v685.Bedrock_v685
import org.cloudburstmc.protocol.bedrock.codec.v686.Bedrock_v686
import org.cloudburstmc.protocol.bedrock.codec.v712.Bedrock_v712
import org.cloudburstmc.protocol.bedrock.codec.v729.Bedrock_v729
import org.cloudburstmc.protocol.bedrock.codec.v748.Bedrock_v748
import org.cloudburstmc.protocol.bedrock.codec.v766.Bedrock_v766
import org.cloudburstmc.protocol.bedrock.codec.v776.Bedrock_v776
import org.cloudburstmc.protocol.bedrock.codec.v786.Bedrock_v786
import org.cloudburstmc.protocol.bedrock.codec.v800.Bedrock_v800
import org.cloudburstmc.protocol.bedrock.codec.v818.Bedrock_v818
import org.cloudburstmc.protocol.bedrock.codec.v819.Bedrock_v819

/**
 * Maps Minecraft version codes to protocol versions and available codecs
 */
object VersionMapper {

    // Default codec to use when version detection fails
    private val DEFAULT_CODEC = Bedrock_v819.CODEC

    // Map of protocol versions to their corresponding codecs
    private val protocolToCodec = mapOf(
        291 to Bedrock_v291.CODEC,
        313 to Bedrock_v313.CODEC,
        332 to Bedrock_v332.CODEC,
        340 to Bedrock_v340.CODEC,
        354 to Bedrock_v354.CODEC,
        361 to Bedrock_v361.CODEC,
        388 to Bedrock_v388.CODEC,
        389 to Bedrock_v389.CODEC,
        390 to Bedrock_v390.CODEC,
        407 to Bedrock_v407.CODEC,
        408 to Bedrock_v408.CODEC,
        419 to Bedrock_v419.CODEC,
        422 to Bedrock_v422.CODEC,
        428 to Bedrock_v428.CODEC,
        431 to Bedrock_v431.CODEC,
        440 to Bedrock_v440.CODEC,
        448 to Bedrock_v448.CODEC,
        465 to Bedrock_v465.CODEC,
        471 to Bedrock_v471.CODEC,
        475 to Bedrock_v475.CODEC,
        486 to Bedrock_v486.CODEC,
        503 to Bedrock_v503.CODEC,
        527 to Bedrock_v527.CODEC,
        534 to Bedrock_v534.CODEC,
        544 to Bedrock_v544.CODEC,
        545 to Bedrock_v545.CODEC,
        554 to Bedrock_v554.CODEC,
        557 to Bedrock_v557.CODEC,
        560 to Bedrock_v560.CODEC,
        567 to Bedrock_v567.CODEC,
        568 to Bedrock_v568.CODEC,
        575 to Bedrock_v575.CODEC,
        582 to Bedrock_v582.CODEC,
        589 to Bedrock_v589.CODEC,
        594 to Bedrock_v594.CODEC,
        618 to Bedrock_v618.CODEC,
        622 to Bedrock_v622.CODEC,
        630 to Bedrock_v630.CODEC,
        649 to Bedrock_v649.CODEC,
        662 to Bedrock_v662.CODEC,
        671 to Bedrock_v671.CODEC,
        685 to Bedrock_v685.CODEC,
        686 to Bedrock_v686.CODEC,
        712 to Bedrock_v712.CODEC,
        729 to Bedrock_v729.CODEC,
        748 to Bedrock_v748.CODEC,
        766 to Bedrock_v766.CODEC,
        776 to Bedrock_v776.CODEC,
        786 to Bedrock_v786.CODEC,
        800 to Bedrock_v800.CODEC,
        818 to Bedrock_v818.CODEC,
        819 to Bedrock_v819.CODEC
    )

    // Map of Minecraft version strings to protocol versions
    private val versionToProtocol = mapOf(
        "1.1.0.55" to 291,
        "1.1.1.51" to 291,
        "1.1.2.50" to 291,
        "1.1.3.52" to 291,
        "1.1.4.51" to 291,
        "1.1.5.0" to 291,
        "1.1.5.1" to 291,
        "1.2.0.81" to 291,
        "1.2.1.1" to 291,
        "1.2.2.3" to 291,
        "1.2.3.6" to 291,
        "1.2.5.52" to 291,
        "1.2.6.55" to 291,
        "1.2.6.60" to 291,
        "1.2.7.2" to 291,
        "1.2.8.0" to 291,
        "1.2.9.1" to 291,
        "1.2.10.2" to 291,
        "1.2.11.4" to 291,
        "1.2.13.54" to 291,
        "1.2.13.60" to 291,
        "1.4.0.5" to 313,
        "1.4.1.0" to 313,
        "1.4.2.0" to 313,
        "1.4.4.0" to 313,
        "1.5.0.14" to 332,
        "1.5.1.2" to 332,
        "1.5.2.1" to 332,
        "1.5.3.0" to 332,
        "1.6.0.14" to 340,
        "1.6.1.0" to 340,
        "1.7.0.13" to 354,
        "1.8.0.24" to 361,
        "1.8.1.2" to 361,
        "1.9.0.15" to 388,
        "1.10.0.7" to 389,
        "1.11.0.23" to 390,
        "1.11.1.2" to 390,
        "1.11.3.1" to 390,
        "1.11.4.2" to 390,
        "1.12.0.28" to 407,
        "1.12.1.1" to 407,
        "1.13.0.34" to 408,
        "1.13.1.5" to 408,
        "1.14.0.9" to 419,
        "1.14.1.4" to 419,
        "1.14.1.5" to 419,
        "1.14.20.1" to 419,
        "1.14.30.2" to 419,
        "1.14.60.5" to 422,
        "1.16.0.2" to 431,
        "1.16.1.02" to 431,
        "1.16.10.02" to 431,
        "1.16.20.03" to 440,
        "1.16.40.02" to 448,
        "1.16.100.04" to 465,
        "1.16.101.01" to 465,
        "1.16.200.02" to 471,
        "1.16.201.01" to 471,
        "1.16.210.05" to 475,
        "1.16.220.02" to 486,
        "1.16.221.01" to 486,
        "1.17.0.02" to 486,
        "1.17.2.01" to 486,
        "1.17.10.04" to 486,
        "1.17.11.01" to 486,
        "1.17.30.04" to 486,
        "1.17.32.02" to 486,
        "1.17.34.02" to 486,
        "1.17.40.06" to 486,
        "1.17.41.01" to 486,
        "1.18.0.02" to 503,
        "1.18.1.02" to 503,
        "1.18.2.03" to 503,
        "1.18.10.04" to 503,
        "1.18.12.01" to 503,
        "1.18.30.04" to 527,
        "1.18.31.04" to 527,
        "1.19.0.05" to 534,
        "1.19.2.02" to 534,
        "1.19.10.03" to 544,
        "1.19.11.01" to 544,
        "1.19.20.02" to 545,
        "1.19.21.01" to 545,
        "1.19.22.01" to 545,
        "1.19.30.04" to 554,
        "1.19.31.01" to 554,
        "1.19.40.02" to 557,
        "1.19.41.01" to 557,
        "1.19.50.02" to 560,
        "1.19.51.01" to 560,
        "1.19.60.03" to 567,
        "1.19.62.01" to 567,
        "1.19.63.01" to 568,
        "1.19.70.02" to 575,
        "1.19.71.02" to 575,
        "1.19.73.02" to 575,
        "1.19.80.02" to 582,
        "1.19.81.01" to 582,
        "1.19.83.01" to 582,
        "1.20.0.01" to 589,
        "1.20.1.02" to 589,
        "1.20.10.01" to 594,
        "1.20.12.01" to 594,
        "1.20.15.01" to 594,
        "1.20.30.02" to 618,
        "1.20.31.01" to 618,
        "1.20.32.03" to 618,
        "1.20.40.01" to 622,
        "1.20.41.02" to 622,
        "1.20.50.03" to 630,
        "1.20.51.01" to 630,
        "1.20.60.04" to 649,
        "1.20.62.02" to 649,
        "1.20.70.06" to 662,
        "1.20.71.01" to 662,
        "1.20.72.01" to 662,
        "1.20.73.01" to 662,
        "1.20.80.05" to 671,
        "1.20.81.01" to 671,
        "1.21.0.03" to 685,
        "1.21.1.03" to 685,
        "1.21.2.02" to 686,
        "1.21.10.24" to 712,
        "1.21.20.03" to 712,
        "1.21.21.01" to 712,
        "1.21.22.01" to 712,
        "1.21.23.01" to 712,
        "1.21.30.03" to 729,
        "1.21.31.04" to 729,
        "1.21.40.04" to 748,
        "1.21.41.01" to 748,
        "1.21.43.01" to 748,
        "1.21.44.01" to 748,
        "1.21.50.07" to 766,
        "1.21.51.01" to 766,
        "1.21.51.02" to 766,
        "1.21.60.10" to 776,
        "1.21.61.01" to 776,
        "1.21.62.01" to 776,
        "1.21.70.03" to 786,
        "1.21.71.01" to 786,
        "1.21.72.01" to 786,
        "1.21.80.3" to 800,
        "1.21.81.2" to 800,
        "1.21.90.3" to 818,
        "1.21.92.1" to 818,
        "1.21.93.1" to 819,
        "1.21.94.1" to 819
    )

    /**
     * Get the appropriate codec for a given Minecraft version string
     */
    fun getCodecForVersion(minecraftVersion: String): BedrockCodec {
        val protocolVersion = versionToProtocol[minecraftVersion]
        return if (protocolVersion != null) {
            protocolToCodec[protocolVersion] ?: DEFAULT_CODEC
        } else {
            println("VersionMapper: Unknown Minecraft version '$minecraftVersion', using default codec")
            DEFAULT_CODEC
        }
    }

    /**
     * Get the appropriate codec for a given protocol version
     */
    fun getCodecForProtocol(protocolVersion: Int): BedrockCodec {
        return protocolToCodec[protocolVersion] ?: run {
            println("VersionMapper: Unknown protocol version '$protocolVersion', using default codec")
            DEFAULT_CODEC
        }
    }

    /**
     * Get the protocol version for a given Minecraft version string
     */
    fun getProtocolForVersion(minecraftVersion: String): Int {
        return versionToProtocol[minecraftVersion] ?: run {
            println("VersionMapper: Unknown Minecraft version '$minecraftVersion', using default protocol")
            DEFAULT_CODEC.protocolVersion
        }
    }

    /**
     * Get all supported Minecraft versions
     */
    fun getSupportedVersions(): Set<String> {
        return versionToProtocol.keys
    }

    /**
     * Get all supported protocol versions
     */
    fun getSupportedProtocols(): Set<Int> {
        return protocolToCodec.keys
    }

    /**
     * Check if a Minecraft version is supported
     */
    fun isVersionSupported(minecraftVersion: String): Boolean {
        return versionToProtocol.containsKey(minecraftVersion)
    }

    /**
     * Check if a protocol version is supported
     */
    fun isProtocolSupported(protocolVersion: Int): Boolean {
        return protocolToCodec.containsKey(protocolVersion)
    }
}