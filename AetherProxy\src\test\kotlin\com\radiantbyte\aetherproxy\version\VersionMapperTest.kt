package com.radiantbyte.aetherproxy.version

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

class VersionMapperTest {

    @Test
    fun testGetCodecForVersion() {
        // Test known versions
        val codec819 = VersionMapper.getCodecForVersion("1.21.93")
        assertEquals(819, codec819.protocolVersion)

        val codec818 = VersionMapper.getCodecForVersion("1.21.90")
        assertEquals(818, codec818.protocolVersion)

        val codec800 = VersionMapper.getCodecForVersion("1.21.80")
        assertEquals(800, codec800.protocolVersion)
    }

    @Test
    fun testGetCodecForProtocol() {
        val codec819 = VersionMapper.getCodecForProtocol(819)
        assertEquals(819, codec819.protocolVersion)

        val codec818 = VersionMapper.getCodecForProtocol(818)
        assertEquals(818, codec818.protocolVersion)
    }

    @Test
    fun testGetProtocolForVersion() {
        assertEquals(819, VersionMapper.getProtocolForVersion("1.21.93"))
        assertEquals(819, VersionMapper.getProtocolForVersion("1.21.94"))
        assertEquals(818, VersionMapper.getProtocolForVersion("1.21.90"))
        assertEquals(800, VersionMapper.getProtocolForVersion("1.21.80"))
    }

    @Test
    fun testIsVersionSupported() {
        assertTrue(VersionMapper.isVersionSupported("1.21.93"))
        assertTrue(VersionMapper.isVersionSupported("1.21.94"))
        assertTrue(VersionMapper.isVersionSupported("1.21.90"))
        assertFalse(VersionMapper.isVersionSupported("1.22.0"))
        assertFalse(VersionMapper.isVersionSupported("unknown"))
    }

    @Test
    fun testIsProtocolSupported() {
        assertTrue(VersionMapper.isProtocolSupported(819))
        assertTrue(VersionMapper.isProtocolSupported(818))
        assertTrue(VersionMapper.isProtocolSupported(800))
        assertFalse(VersionMapper.isProtocolSupported(999))
    }

    @Test
    fun testUnknownVersionFallback() {
        // Unknown version should return default codec
        val unknownCodec = VersionMapper.getCodecForVersion("unknown.version")
        assertEquals(819, unknownCodec.protocolVersion) // Default is v819

        val unknownProtocol = VersionMapper.getProtocolForVersion("unknown.version")
        assertEquals(819, unknownProtocol) // Default protocol
    }

    @Test
    fun testGetSupportedVersions() {
        val supportedVersions = VersionMapper.getSupportedVersions()
        assertTrue(supportedVersions.contains("1.21.93"))
        assertTrue(supportedVersions.contains("1.21.94"))
        assertTrue(supportedVersions.contains("1.21.90"))
        assertTrue(supportedVersions.size > 50) // Should have many versions
    }

    @Test
    fun testGetSupportedProtocols() {
        val supportedProtocols = VersionMapper.getSupportedProtocols()
        assertTrue(supportedProtocols.contains(819))
        assertTrue(supportedProtocols.contains(818))
        assertTrue(supportedProtocols.contains(800))
        assertTrue(supportedProtocols.size > 30) // Should have many protocols
    }
}