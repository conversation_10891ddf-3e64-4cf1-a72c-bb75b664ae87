  Test #com.radiantbyte.aetherproxy.version  
VersionMapper #com.radiantbyte.aetherproxy.version  VersionMapperTest #com.radiantbyte.aetherproxy.version  assertEquals #com.radiantbyte.aetherproxy.version  assertFalse #com.radiantbyte.aetherproxy.version  
assertTrue #com.radiantbyte.aetherproxy.version  getCodecForProtocol #com.radiantbyte.aetherproxy.version  getCodecForVersion #com.radiantbyte.aetherproxy.version  getProtocolForVersion #com.radiantbyte.aetherproxy.version  getSupportedProtocols #com.radiantbyte.aetherproxy.version  getSupportedVersions #com.radiantbyte.aetherproxy.version  isProtocolSupported #com.radiantbyte.aetherproxy.version  isVersionSupported #com.radiantbyte.aetherproxy.version  getCodecForProtocol 1com.radiantbyte.aetherproxy.version.VersionMapper  getCodecForVersion 1com.radiantbyte.aetherproxy.version.VersionMapper  getProtocolForVersion 1com.radiantbyte.aetherproxy.version.VersionMapper  getSupportedProtocols 1com.radiantbyte.aetherproxy.version.VersionMapper  getSupportedVersions 1com.radiantbyte.aetherproxy.version.VersionMapper  isProtocolSupported 1com.radiantbyte.aetherproxy.version.VersionMapper  isVersionSupported 1com.radiantbyte.aetherproxy.version.VersionMapper  
VersionMapper 5com.radiantbyte.aetherproxy.version.VersionMapperTest  assertEquals 5com.radiantbyte.aetherproxy.version.VersionMapperTest  assertFalse 5com.radiantbyte.aetherproxy.version.VersionMapperTest  
assertTrue 5com.radiantbyte.aetherproxy.version.VersionMapperTest  getCodecForProtocol 5com.radiantbyte.aetherproxy.version.VersionMapperTest  getCodecForVersion 5com.radiantbyte.aetherproxy.version.VersionMapperTest  getProtocolForVersion 5com.radiantbyte.aetherproxy.version.VersionMapperTest  getSupportedProtocols 5com.radiantbyte.aetherproxy.version.VersionMapperTest  getSupportedVersions 5com.radiantbyte.aetherproxy.version.VersionMapperTest  isProtocolSupported 5com.radiantbyte.aetherproxy.version.VersionMapperTest  isVersionSupported 5com.radiantbyte.aetherproxy.version.VersionMapperTest  	compareTo 
kotlin.Int  Set kotlin.collections  contains kotlin.collections.Set  size kotlin.collections.Set  BedrockCodec 'org.cloudburstmc.protocol.bedrock.codec  protocolVersion 4org.cloudburstmc.protocol.bedrock.codec.BedrockCodec  Test org.junit.jupiter.api  
VersionMapper org.junit.jupiter.api  assertEquals org.junit.jupiter.api  assertFalse org.junit.jupiter.api  
assertTrue org.junit.jupiter.api  getCodecForProtocol org.junit.jupiter.api  getCodecForVersion org.junit.jupiter.api  getProtocolForVersion org.junit.jupiter.api  getSupportedProtocols org.junit.jupiter.api  getSupportedVersions org.junit.jupiter.api  isProtocolSupported org.junit.jupiter.api  isVersionSupported org.junit.jupiter.api  assertEquals  org.junit.jupiter.api.Assertions  assertFalse  org.junit.jupiter.api.Assertions  
assertTrue  org.junit.jupiter.api.Assertions  VersionCodeMapper #com.radiantbyte.aetherproxy.version  VersionCodeMapperTest #com.radiantbyte.aetherproxy.version  
assertNotNull #com.radiantbyte.aetherproxy.version  
assertNull #com.radiantbyte.aetherproxy.version  getBestMatchingVersion #com.radiantbyte.aetherproxy.version  getMinecraftVersionForCode #com.radiantbyte.aetherproxy.version  getSupportedVersionCodes #com.radiantbyte.aetherproxy.version  isVersionCodeSupported #com.radiantbyte.aetherproxy.version  getBestMatchingVersion 5com.radiantbyte.aetherproxy.version.VersionCodeMapper  getMinecraftVersionForCode 5com.radiantbyte.aetherproxy.version.VersionCodeMapper  getSupportedVersionCodes 5com.radiantbyte.aetherproxy.version.VersionCodeMapper  isVersionCodeSupported 5com.radiantbyte.aetherproxy.version.VersionCodeMapper  VersionCodeMapper 9com.radiantbyte.aetherproxy.version.VersionCodeMapperTest  assertEquals 9com.radiantbyte.aetherproxy.version.VersionCodeMapperTest  assertFalse 9com.radiantbyte.aetherproxy.version.VersionCodeMapperTest  
assertNotNull 9com.radiantbyte.aetherproxy.version.VersionCodeMapperTest  
assertNull 9com.radiantbyte.aetherproxy.version.VersionCodeMapperTest  
assertTrue 9com.radiantbyte.aetherproxy.version.VersionCodeMapperTest  getBestMatchingVersion 9com.radiantbyte.aetherproxy.version.VersionCodeMapperTest  getMinecraftVersionForCode 9com.radiantbyte.aetherproxy.version.VersionCodeMapperTest  getSupportedVersionCodes 9com.radiantbyte.aetherproxy.version.VersionCodeMapperTest  isVersionCodeSupported 9com.radiantbyte.aetherproxy.version.VersionCodeMapperTest  VersionCodeMapper org.junit.jupiter.api  
assertNotNull org.junit.jupiter.api  
assertNull org.junit.jupiter.api  getBestMatchingVersion org.junit.jupiter.api  getMinecraftVersionForCode org.junit.jupiter.api  getSupportedVersionCodes org.junit.jupiter.api  isVersionCodeSupported org.junit.jupiter.api  
assertNotNull  org.junit.jupiter.api.Assertions  
assertNull  org.junit.jupiter.api.Assertions                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                