<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.radiantbyte.aetherproxy.version.VersionMapperTest" tests="8" skipped="0" failures="0" errors="0" timestamp="2025-07-28T16:49:43" hostname="DESKTOP-G21U0DT" time="0.754">
  <properties/>
  <testcase name="testIsVersionSupported()" classname="com.radiantbyte.aetherproxy.version.VersionMapperTest" time="0.747"/>
  <testcase name="testGetSupportedVersions()" classname="com.radiantbyte.aetherproxy.version.VersionMapperTest" time="0.0"/>
  <testcase name="testUnknownVersionFallback()" classname="com.radiantbyte.aetherproxy.version.VersionMapperTest" time="0.002"/>
  <testcase name="testGetProtocolForVersion()" classname="com.radiantbyte.aetherproxy.version.VersionMapperTest" time="0.001"/>
  <testcase name="testIsProtocolSupported()" classname="com.radiantbyte.aetherproxy.version.VersionMapperTest" time="0.0"/>
  <testcase name="testGetCodecForVersion()" classname="com.radiantbyte.aetherproxy.version.VersionMapperTest" time="0.0"/>
  <testcase name="testGetCodecForProtocol()" classname="com.radiantbyte.aetherproxy.version.VersionMapperTest" time="0.001"/>
  <testcase name="testGetSupportedProtocols()" classname="com.radiantbyte.aetherproxy.version.VersionMapperTest" time="0.0"/>
  <system-out><![CDATA[[22:49:44 DEBUG]: Using Log4J2 as the default logging framework
[22:49:44 DEBUG]: -Dio.netty.noUnsafe: false
[22:49:44 DEBUG]: Java version: 21
[22:49:44 DEBUG]: sun.misc.Unsafe.theUnsafe: available
[22:49:44 DEBUG]: sun.misc.Unsafe base methods: all available
[22:49:44 DEBUG]: java.nio.Buffer.address: available
[22:49:44 DEBUG]: direct buffer constructor: unavailable: Reflective setAccessible(true) disabled
[22:49:44 DEBUG]: java.nio.Bits.unaligned: available, true
[22:49:44 DEBUG]: jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable: symbolic reference class is not accessible: class jdk.internal.misc.Unsafe, from class io.netty.util.internal.PlatformDependent0 (unnamed module @3d6f0054)
[22:49:44 DEBUG]: java.nio.DirectByteBuffer.<init>(long, {int,long}): unavailable
[22:49:44 DEBUG]: sun.misc.Unsafe: available
[22:49:44 DEBUG]: -Dio.netty.tmpdir: C:\Users\<USER>\AppData\Local\Temp (java.io.tmpdir)
[22:49:44 DEBUG]: -Dio.netty.bitMode: 64 (sun.arch.data.model)
[22:49:44 DEBUG]: Platform: Windows
[22:49:44 DEBUG]: -Dio.netty.maxDirectMemory: -1 bytes
[22:49:44 DEBUG]: java.nio.ByteBuffer.cleaner(): available
[22:49:44 DEBUG]: -Dio.netty.noPreferDirect: false
VersionMapper: Unknown Minecraft version 'unknown.version', using default codec
VersionMapper: Unknown Minecraft version 'unknown.version', using default protocol
]]></system-out>
  <system-err><![CDATA[SLF4J(W): No SLF4J providers were found.
SLF4J(W): Defaulting to no-operation (NOP) logger implementation
SLF4J(W): See https://www.slf4j.org/codes.html#noProviders for further details.
]]></system-err>
</testsuite>
