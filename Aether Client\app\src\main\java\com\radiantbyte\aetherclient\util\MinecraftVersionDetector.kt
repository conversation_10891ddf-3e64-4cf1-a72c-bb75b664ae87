package com.radiantbyte.aetherclient.util

import android.content.Context
import android.content.pm.PackageManager
import android.os.Build

/**
 * Utility class to detect the installed Minecraft version and its version code
 */
object MinecraftVersionDetector {

    private const val MINECRAFT_PACKAGE_NAME = "com.mojang.minecraftpe"

    /**
     * Data class to hold version information
     */
    data class MinecraftVersionInfo(
        val versionName: String,
        val versionCode: Long,
        val isInstalled: <PERSON>olean
    )

    /**
     * Detect the installed Minecraft version
     */
    fun detectMinecraftVersion(context: Context): MinecraftVersionInfo {
        return try {
            val packageManager = context.packageManager
            val packageInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                packageManager.getPackageInfo(
                    MINECRAFT_PACKAGE_NAME,
                    PackageManager.PackageInfoFlags.of(0)
                )
            } else {
                @Suppress("DEPRECATION")
                packageManager.getPackageInfo(MINECRAFT_PACKAGE_NAME, 0)
            }

            val versionName = packageInfo.versionName ?: "Unknown"
            val versionCode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageInfo.longVersionCode
            } else {
                @Suppress("DEPRECATION")
                packageInfo.versionCode.toLong()
            }

            println("MinecraftVersionDetector: Detected Minecraft version: $versionName (code: $versionCode)")

            MinecraftVersionInfo(
                versionName = versionName,
                versionCode = versionCode,
                isInstalled = true
            )
        } catch (e: PackageManager.NameNotFoundException) {
            println("MinecraftVersionDetector: Minecraft not installed")
            MinecraftVersionInfo(
                versionName = "Not Installed",
                versionCode = 0,
                isInstalled = false
            )
        } catch (e: Exception) {
            println("MinecraftVersionDetector: Error detecting Minecraft version: ${e.message}")
            MinecraftVersionInfo(
                versionName = "Error",
                versionCode = 0,
                isInstalled = false
            )
        }
    }


}