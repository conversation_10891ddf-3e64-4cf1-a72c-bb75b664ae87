<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - VersionMapperTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>VersionMapperTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.radiantbyte.aetherproxy.version.html">com.radiantbyte.aetherproxy.version</a> &gt; VersionMapperTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">8</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.751s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Tests</a>
</li>
<li>
<a href="#tab1">Standard output</a>
</li>
<li>
<a href="#tab2">Standard error</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testGetCodecForProtocol()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetCodecForVersion()</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetProtocolForVersion()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetSupportedProtocols()</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetSupportedVersions()</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testIsProtocolSupported()</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testIsVersionSupported()</td>
<td class="success">0.747s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testUnknownVersionFallback()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div id="tab1" class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>[22:49:44 DEBUG]: Using Log4J2 as the default logging framework
[22:49:44 DEBUG]: -Dio.netty.noUnsafe: false
[22:49:44 DEBUG]: Java version: 21
[22:49:44 DEBUG]: sun.misc.Unsafe.theUnsafe: available
[22:49:44 DEBUG]: sun.misc.Unsafe base methods: all available
[22:49:44 DEBUG]: java.nio.Buffer.address: available
[22:49:44 DEBUG]: direct buffer constructor: unavailable: Reflective setAccessible(true) disabled
[22:49:44 DEBUG]: java.nio.Bits.unaligned: available, true
[22:49:44 DEBUG]: jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable: symbolic reference class is not accessible: class jdk.internal.misc.Unsafe, from class io.netty.util.internal.PlatformDependent0 (unnamed module @3d6f0054)
[22:49:44 DEBUG]: java.nio.DirectByteBuffer.&lt;init&gt;(long, {int,long}): unavailable
[22:49:44 DEBUG]: sun.misc.Unsafe: available
[22:49:44 DEBUG]: -Dio.netty.tmpdir: C:\Users\<USER>\AppData\Local\Temp (java.io.tmpdir)
[22:49:44 DEBUG]: -Dio.netty.bitMode: 64 (sun.arch.data.model)
[22:49:44 DEBUG]: Platform: Windows
[22:49:44 DEBUG]: -Dio.netty.maxDirectMemory: -1 bytes
[22:49:44 DEBUG]: java.nio.ByteBuffer.cleaner(): available
[22:49:44 DEBUG]: -Dio.netty.noPreferDirect: false
VersionMapper: Unknown Minecraft version 'unknown.version', using default codec
VersionMapper: Unknown Minecraft version 'unknown.version', using default protocol
</pre>
</span>
</div>
<div id="tab2" class="tab">
<h2>Standard error</h2>
<span class="code">
<pre>SLF4J(W): No SLF4J providers were found.
SLF4J(W): Defaulting to no-operation (NOP) logger implementation
SLF4J(W): See https://www.slf4j.org/codes.html#noProviders for further details.
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.10</a> at Jul 28, 2025, 10:49:44 PM</p>
</div>
</div>
</body>
</html>
